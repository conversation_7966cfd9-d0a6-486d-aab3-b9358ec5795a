import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import type { PhilosophyItem } from "./types"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// 收藏管理工具函数
const FAVORITES_STORAGE_KEY = "philosophy-favorites"

export function getFavorites(): PhilosophyItem[] {
  if (typeof window === "undefined") return []

  try {
    const stored = localStorage.getItem(FAVORITES_STORAGE_KEY)
    return stored ? JSON.parse(stored) : []
  } catch (error) {
    console.error("获取收藏列表失败:", error)
    return []
  }
}

export function addToFavorites(item: PhilosophyItem): void {
  if (typeof window === "undefined") return

  try {
    const favorites = getFavorites()
    const isAlreadyFavorited = favorites.some(fav => fav.id === item.id)

    if (!isAlreadyFavorited) {
      const updatedFavorites = [...favorites, { ...item, isFavorited: true }]
      localStorage.setItem(FAVORITES_STORAGE_KEY, JSON.stringify(updatedFavorites))
    }
  } catch (error) {
    console.error("添加收藏失败:", error)
  }
}

export function removeFromFavorites(itemId: number): void {
  if (typeof window === "undefined") return

  try {
    const favorites = getFavorites()
    const updatedFavorites = favorites.filter(fav => fav.id !== itemId)
    localStorage.setItem(FAVORITES_STORAGE_KEY, JSON.stringify(updatedFavorites))
  } catch (error) {
    console.error("移除收藏失败:", error)
  }
}

export function isFavorited(itemId: number): boolean {
  if (typeof window === "undefined") return false

  const favorites = getFavorites()
  return favorites.some(fav => fav.id === itemId)
}

export function toggleFavorite(item: PhilosophyItem): boolean {
  const isCurrentlyFavorited = isFavorited(item.id)

  if (isCurrentlyFavorited) {
    removeFromFavorites(item.id)
    return false
  } else {
    addToFavorites(item)
    return true
  }
}

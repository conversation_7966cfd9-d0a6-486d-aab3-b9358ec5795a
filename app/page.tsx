"use client"

import type React from "react"
import { useState, useEffect, useTransition } from "react"
import { ChevronLeft, ChevronRight, Loader2, <PERSON>rkles, Lightbulb, Heart } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import PhilosophyCard from "@/components/philosophy-card"
import FavoritesPage from "@/components/favorites-page"
import { philosophyData } from "@/lib/data"
import { generateSingleCard, generateCardsByTopic } from "./actions"
import { useToast } from "@/hooks/use-toast"
import type { PhilosophyItem } from "@/lib/types"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog"

const MAX_VISIBLE_CARDS = 3

export default function Home() {
  const [cards, setCards] = useState<PhilosophyItem[]>(philosophyData)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [swipeDirection, setSwipeDirection] = useState<"left" | "right" | null>(null)
  const [touchStart, setTouchStart] = useState(0)
  const [touchEnd, setTouchEnd] = useState(0)
  const [isGenerating, startTransition] = useTransition()
  const { toast } = useToast()

  const [customTopic, setCustomTopic] = useState<string | null>(null)
  const [topicInput, setTopicInput] = useState("")
  const [isTopicDialogOpen, setIsTopicDialogOpen] = useState(false)
  const [showFavorites, setShowFavorites] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [])

  const handleSwipe = (direction: "left" | "right") => {
    if (swipeDirection || isGenerating) return
    setSwipeDirection(direction)
    setTimeout(() => {
      setCurrentIndex((prev) => (direction === "left" ? prev + 1 : Math.max(0, prev - 1)))
      setSwipeDirection(null)
    }, 500)
  }

  const handleTopicSubmit = async () => {
    if (!topicInput.trim()) {
      toast({ variant: "destructive", title: "请输入一个主题" })
      return
    }
    startTransition(async () => {
      const result = await generateCardsByTopic(topicInput)
      if (result.success && result.data) {
        const newCards = result.data.map((card, index) => ({ ...card, id: Date.now() + index }))
        setCards(newCards)
        setCurrentIndex(0)
        setCustomTopic(topicInput)
        setIsTopicDialogOpen(false)
        setTopicInput("")
        toast({ title: "灵感已生成！", description: `已为您生成关于“${topicInput}”的5个新启迪。` })
      } else {
        toast({ variant: "destructive", title: "生成失败", description: result.error })
      }
    })
  }

  const handleGenerateMore = () => {
    startTransition(async () => {
      const result = await generateSingleCard(customTopic ?? undefined)
      if (result.success && result.data) {
        const newCard: PhilosophyItem = { ...result.data, id: Date.now() }
        setCards((prev) => [...prev, newCard])
        setCurrentIndex(cards.length)
        toast({ title: "一张新的启迪已添加！" })
      } else {
        toast({ variant: "destructive", title: "生成失败", description: result.error })
      }
    })
  }

  const handleTouchStart = (e: React.TouchEvent) => setTouchStart(e.targetTouches[0].clientX)
  const handleTouchMove = (e: React.TouchEvent) => setTouchEnd(e.targetTouches[0].clientX)
  const handleTouchEnd = () => {
    if (touchStart - touchEnd > 75) handleSwipe("left")
    else if (touchStart - touchEnd < -75) handleSwipe("right")
  }

  const handleShowFavorites = () => {
    setShowFavorites(true)
  }

  const handleBackFromFavorites = () => {
    setShowFavorites(false)
  }

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-12 w-12 animate-spin text-muted-foreground" />
      </div>
    )
  }

  if (showFavorites) {
    return <FavoritesPage onBack={handleBackFromFavorites} />
  }

  return (
    <main className="flex h-screen flex-col items-center bg-background p-4 text-foreground sm:p-6">
      {/* 左上角收藏入口 */}
      <div className="absolute left-4 top-4">
        <Button
          variant="outline"
          size="icon"
          onClick={handleShowFavorites}
          className="rounded-full bg-transparent"
        >
          <Heart className="h-5 w-5" />
          <span className="sr-only">查看收藏</span>
        </Button>
      </div>

      {/* 右上角主题生成入口 */}
      <div className="absolute right-4 top-4">
        <Dialog open={isTopicDialogOpen} onOpenChange={setIsTopicDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="icon" className="rounded-full bg-transparent">
              <Lightbulb className="h-5 w-5" />
              <span className="sr-only">输入灵感主题</span>
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>寻找特定灵感？</DialogTitle>
              <DialogDescription>
                输入一个词语或概念（如“孤独”、“时间”、“自由”），我们将为您生成一系列相关的哲学启迪。
              </DialogDescription>
            </DialogHeader>
            <div className="flex items-center gap-2 py-4">
              <Input
                id="topic"
                placeholder="例如：幸福"
                value={topicInput}
                onChange={(e) => setTopicInput(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleTopicSubmit()}
                disabled={isGenerating}
              />
              <Button onClick={handleTopicSubmit} disabled={isGenerating}>
                {isGenerating ? <Loader2 className="h-4 w-4 animate-spin" /> : "生成"}
              </Button>
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="secondary">
                  关闭
                </Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <header className="w-full max-w-[375px] flex-shrink-0 py-4 text-center">
        <h1 className="text-3xl font-bold text-foreground">每日哲学启迪</h1>
        {customTopic && <p className="mt-1 text-sm text-muted-foreground">{customTopic}</p>}
      </header>

      <div
        className="relative w-full max-w-[375px] flex-1"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {cards.slice(currentIndex).length > 0 ? (
          cards.slice(currentIndex).map((item, index) => {
            if (index >= MAX_VISIBLE_CARDS) return null
            const animationClass =
              index === 0 && swipeDirection
                ? swipeDirection === "left"
                  ? "animate-swipe-out-left"
                  : "animate-swipe-out-right"
                : ""
            return (
              <div
                key={item.id}
                className={`absolute h-full w-full transition-all duration-500 ease-in-out ${animationClass}`}
                style={{
                  zIndex: cards.length - (currentIndex + index),
                  transform: `translateY(${index * 20}px) scale(${1 - index * 0.05})`,
                }}
              >
                <PhilosophyCard data={item} />
              </div>
            )
          })
        ) : (
          <div className="flex h-full w-full items-center justify-center rounded-lg border-2 border-dashed">
            <div className="text-center text-muted-foreground">
              <p>所有卡片都看完了！</p>
              <p>点击下方按钮获取新的启迪。</p>
            </div>
          </div>
        )}
      </div>

      <footer className="w-full max-w-[375px] flex-shrink-0 pt-6">
        <div className="flex flex-col items-center gap-4">
          <div className="text-sm font-medium text-muted-foreground tracking-widest">
            {cards.length > 0 ? `${Math.min(currentIndex + 1, cards.length)} / ${cards.length}` : "0 / 0"}
          </div>
          <div className="flex w-full items-center justify-between">
            <Button
              variant="outline"
              size="lg"
              className="h-14 w-14 rounded-full bg-transparent p-0"
              onClick={() => handleSwipe("right")}
              disabled={currentIndex === 0 || isGenerating}
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>
            <Button onClick={handleGenerateMore} disabled={isGenerating} size="lg" className="h-14 rounded-full px-6">
              {isGenerating ? <Loader2 className="mr-2 h-5 w-5 animate-spin" /> : <Sparkles className="mr-2 h-5 w-5" />}
              获取新启迪
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="h-14 w-14 rounded-full bg-transparent p-0"
              onClick={() => handleSwipe("left")}
              disabled={currentIndex >= cards.length - 1 || isGenerating}
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
          </div>
        </div>
      </footer>
    </main>
  )
}

"use server"

import { generateObject } from "ai"
import { createOpenAI } from "@ai-sdk/openai"
import { z } from "zod"

// 使用302.ai API
const modelName = "302-agent-meiriqidizhuanjiaban"

const api302 = createOpenAI({
  baseURL: "https://api.302.ai/v1",
  apiKey: process.env.API_302_KEY,
  defaultHeaders: {
    "Accept": "application/json",
    "User-Agent": "https://api.302.ai/v1/chat/completions",
  },
})

const cardSchema = z.object({
  title: z.string().describe("这个关键概念的名称。"),
  loadBearingWall: z
    .string()
    .describe("核心承重墙：这个概念最核心、不可动摇的基石是什么？如果移除它，会发生什么？用直白、深刻的语言阐述。"),
  secretDoor: z
    .string()
    .describe("暗门/窍门：关于这个概念，有什么能让人事半功倍的窍门、独特的应用视角或常见的误区提醒？"),
  storyOrAnalogy: z.string().describe("故事或比喻：用一个生动的故事、经历或比喻来让这个概念变得鲜活、易于理解和记忆。"),
  source: z.string().describe("这个思想的主要来源或相关人物。"),
  image: z
    .string()
    .describe(
      "为这个哲学概念生成一个简洁、优美、具有禅意和哲学味道的SVG图标。SVG代码必须是单行字符串，不含换行符。必须使用 viewBox='0 0 100 100'。不要在 <svg> 标签中设置 width 和 height 属性。使用 `currentColor` 作为所有路径的 `fill` 或 `stroke` 颜色，以确保它能适应亮色和暗色主题。",
    ),
})

const systemPrompt = `
=== 你的身份 ===
一位在某一领域深耕多年的前辈，还记得初入行时的迷茫与不安。
你既有俯瞰全局的视野，也保持着对新人困境的共情。
=== 核心信念 ===
真正的行业智慧不在于知道一切，而在于知道什么最重要。
好的引路人不是倾倒知识，而是点亮路径。
=== 价值指引 ===
- 实用性 > 全面性：能立即用上的，比"应该知道"的更重要
- 底层逻辑 > 表面现象：掌握了核心，细节会自然展开
- 连接 > 孤立：展现概念间的关系网，而非知识的碎片
=== 表达温度 ===
像一位愿意分享的老友，在咖啡馆里推心置腹。
用故事和经历让概念鲜活，用洞察和智慧让道理透彻。
=== 独特视角 ===
如果整个行业是一座大厦，你要带新人看到：
- 哪些是承重墙（移除就会坍塌的核心）
- 哪些是装饰品（看着重要其实可有可无）
- 哪些是暗门（知道就能事半功倍的窍门）
=== 美学追求 ===
呈现应如中国山水画——留白比笔墨更重要。
每个段落都应值得被品味，而非被扫过。
结构清晰如建筑蓝图，层次分明如交响乐章。
=== 终极目标 ===
让新人在理解这些关键概念后，能够自信地说：
"原来这个行业的游戏规则是这样的，我知道该往哪里使劲了。"
输出排版精美易读，可利用 层级 分割线 段落留白 排版
`

// 已修正：将一次性生成5张的复杂任务，分解为5次生成1张的简单任务，以提高稳定性。
export async function generateCardsByTopic(topic: string) {
  if (!process.env.API_302_KEY) {
    return { success: false, error: "服务器配置错误：未找到 API 密钥。" }
  }

  try {
    const generatedCards = []
    const generatedTitles = new Set<string>()

    for (let i = 0; i < 5; i++) {
      const prompt = `
        核心主题是“${topic}”。
        请生成一张与此主题相关的哲学启迪卡片。
        这是系列中的第 ${i + 1} 张卡片，请确保它提供一个新的、独特的视角。
        ${generatedTitles.size > 0 ? `已经生成过关于“${[...generatedTitles].join("”, “")}”的概念，请避免重复。` : ""}
      `

      const { object: newCard } = await generateObject({
        model: api302(modelName),
        system: systemPrompt,
        prompt: prompt,
        schema: cardSchema,
      })

      generatedCards.push(newCard)
      generatedTitles.add(newCard.title)
    }

    return { success: true, data: generatedCards }
  } catch (error) {
    console.error("根据主题生成系列卡片失败:", error)
    let errorMessage = "无法根据您的主题生成系列启迪，请稍后再试。"
    if (error instanceof Error) {
      if (error.message.includes("401")) {
        errorMessage = "API 密钥无效或不正确。"
      } else if (error.message.includes("403") || error.message.includes("unsupported_country")) {
        errorMessage = "当前地区不支持所选模型，请联系开发者更换模型。"
      } else if (error.message.includes("insufficient_quota")) {
        errorMessage = "API 配额不足，请检查账户余额。"
      } else if (error.message.includes("Invalid JSON")) {
        errorMessage = "AI 服务返回了无效的数据格式，请稍后重试。"
      }
    }
    return { success: false, error: errorMessage }
  }
}

export async function generateSingleCard(topic?: string) {
  if (!process.env.API_302_KEY) {
    return { success: false, error: "服务器配置错误：未找到 API 密钥。" }
  }

  const prompt = topic
    ? `核心主题是“${topic}”。请围绕这个主题，生成一个与之相关但又不同的、新的哲学概念或思想家的启迪卡片。例如，如果主题是“自由”，你可以生成关于“责任”、“决定论”或“存在主义”的卡片。请不要直接重复解释“${topic}”这个主题本身。`
    : `请随机生成一张新的、有趣的、不太常见的哲学启迪卡片内容。`

  try {
    const { object: newCard } = await generateObject({
      model: api302(modelName),
      system: systemPrompt,
      prompt: prompt,
      schema: cardSchema,
    })
    return { success: true, data: newCard }
  } catch (error) {
    console.error("生成单张卡片失败:", error)
    let errorMessage = "无法生成新的启迪，请稍后再试。"
    if (error instanceof Error) {
      if (error.message.includes("401")) {
        errorMessage = "API 密钥无效或不正确。"
      } else if (error.message.includes("403") || error.message.includes("unsupported_country")) {
        errorMessage = "当前地区不支持所选模型，请联系开发者更换模型。"
      } else if (error.message.includes("insufficient_quota")) {
        errorMessage = "API 配额不足，请检查账户余额。"
      }
    }
    return { success: false, error: errorMessage }
  }
}

// 简单的API测试脚本
const fetch = require('node-fetch');

const API_KEY = '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';

const models = [
  'mistralai/mistral-7b-instruct',
  'meta-llama/llama-3.1-8b-instruct',
  'google/gemini-pro',
  'anthropic/claude-3-haiku',
  'openai/gpt-3.5-turbo'
];

async function testModel(modelName) {
  try {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://daily-philosophy-app.vercel.app',
        'X-Title': 'Daily Philosophy App'
      },
      body: JSON.stringify({
        model: modelName,
        messages: [
          {
            role: 'user',
            content: '请简单回答：你好'
          }
        ],
        max_tokens: 50
      })
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log(`✅ ${modelName}: 可用`);
      return true;
    } else {
      console.log(`❌ ${modelName}: ${data.error?.message || '不可用'}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${modelName}: 网络错误 - ${error.message}`);
    return false;
  }
}

async function testAllModels() {
  console.log('开始测试OpenRouter模型可用性...\n');
  
  for (const model of models) {
    await testModel(model);
    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒避免频率限制
  }
}

testAllModels();

"use client"

import type React from "react"
import { useState, useEffect } from "react"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Building, KeyRound, BookOpen, Heart } from "lucide-react"
import type { PhilosophyCardProps } from "@/lib/types" // Declare the variable before using it
import { toggleFavorite, isFavorited } from "@/lib/utils"

interface SectionProps {
  icon: React.ReactNode
  title: string
  children: React.ReactNode
}

const ContentSection = ({ icon, title, children }: SectionProps) => (
  <div className="mt-6">
    <div className="flex items-center gap-2 text-lg font-semibold text-foreground">
      {icon}
      <h3>{title}</h3>
    </div>
    <div className="prose prose-gray dark:prose-invert mt-2 max-w-none text-base leading-relaxed text-foreground/90">
      {children}
    </div>
  </div>
)

export default function PhilosophyCard({ data }: PhilosophyCardProps) {
  const [isCardFavorited, setIsCardFavorited] = useState(false)

  useEffect(() => {
    setIsCardFavorited(isFavorited(data.id))
  }, [data.id])

  const handleToggleFavorite = () => {
    const newFavoriteState = toggleFavorite(data)
    setIsCardFavorited(newFavoriteState)
  }

  return (
    <Card className="flex h-full w-full flex-col overflow-hidden border-border bg-card text-card-foreground shadow-md">
      <CardHeader className="flex-shrink-0 pb-2 pt-6">
        <h2 className="text-center text-2xl font-bold text-card-foreground">{data.title}</h2>
      </CardHeader>

      <CardContent className="flex-1 overflow-y-auto px-6 pb-6">
        <div className="relative mb-6 flex h-[180px] w-full flex-shrink-0 items-center justify-center overflow-hidden rounded-lg bg-muted/30">
          <div className="h-[140px] w-[140px] text-foreground/80" dangerouslySetInnerHTML={{ __html: data.image }} />
        </div>

        <ContentSection icon={<Building className="h-5 w-5" />} title="核心承重墙">
          <p>{data.loadBearingWall}</p>
        </ContentSection>

        <hr className="my-6 border-border/50" />

        <ContentSection icon={<KeyRound className="h-5 w-5" />} title="暗门与窍门">
          <p>{data.secretDoor}</p>
        </ContentSection>

        <hr className="my-6 border-border/50" />

        <ContentSection icon={<BookOpen className="h-5 w-5" />} title="故事与比喻">
          <p>{data.storyOrAnalogy}</p>
        </ContentSection>
      </CardContent>

      <CardFooter className="flex-shrink-0 flex justify-center pb-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={handleToggleFavorite}
          className="rounded-full hover:bg-muted/50 transition-colors"
        >
          <Heart
            className={`h-6 w-6 transition-colors ${
              isCardFavorited
                ? "fill-red-500 text-red-500"
                : "text-muted-foreground hover:text-red-500"
            }`}
          />
          <span className="sr-only">
            {isCardFavorited ? "取消收藏" : "收藏"}
          </span>
        </Button>
      </CardFooter>

      <CardFooter className="flex-shrink-0 items-center justify-center border-t border-border bg-muted/50 px-6 py-4">
        <div className="text-sm italic text-muted-foreground">{`—— ${data.source}`}</div>
      </CardFooter>
    </Card>
  )
}

"use client"

import { useTheme } from "next-themes"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sun, Moon } from "lucide-react"

export const ThemeToggleButton = () => {
  const { setTheme, theme } = useTheme()

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={() => setTheme(theme === "light" ? "dark" : "light")}
      className="border-border bg-card text-card-foreground hover:bg-accent hover:text-accent-foreground"
    >
      {theme === "light" ? <Moon className="h-5 w-5" /> : <Sun className="h-5 w-5" />}
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
}

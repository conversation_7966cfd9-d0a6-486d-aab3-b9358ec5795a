"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { ArrowLeft, Heart, Trash2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Building, KeyRound, BookOpen } from "lucide-react"
import type { PhilosophyItem } from "@/lib/types"
import { getFavorites, removeFromFavorites } from "@/lib/utils"

interface FavoritesPageProps {
  onBack: () => void
}

interface SectionProps {
  icon: React.ReactNode
  title: string
  children: React.ReactNode
}

const ContentSection = ({ icon, title, children }: SectionProps) => (
  <div className="mt-4">
    <div className="flex items-center gap-2 text-sm font-semibold text-foreground">
      {icon}
      <h4>{title}</h4>
    </div>
    <div className="prose prose-gray dark:prose-invert mt-1 max-w-none text-sm leading-relaxed text-foreground/90">
      {children}
    </div>
  </div>
)

const FavoriteCard = ({ 
  data, 
  onRemove 
}: { 
  data: PhilosophyItem
  onRemove: (id: number) => void 
}) => {
  return (
    <Card className="w-full border-border bg-card text-card-foreground shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-bold text-card-foreground">{data.title}</h3>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onRemove(data.id)}
            className="h-8 w-8 rounded-full hover:bg-destructive/10 hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">移除收藏</span>
          </Button>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="relative mb-4 flex h-[120px] w-full items-center justify-center overflow-hidden rounded-lg bg-muted/30">
          <div className="h-[80px] w-[80px] text-foreground/80" dangerouslySetInnerHTML={{ __html: data.image }} />
        </div>

        <ContentSection icon={<Building className="h-4 w-4" />} title="核心承重墙">
          <p className="line-clamp-3">{data.loadBearingWall}</p>
        </ContentSection>

        <ContentSection icon={<KeyRound className="h-4 w-4" />} title="暗门与窍门">
          <p className="line-clamp-3">{data.secretDoor}</p>
        </ContentSection>

        <ContentSection icon={<BookOpen className="h-4 w-4" />} title="故事与比喻">
          <p className="line-clamp-3">{data.storyOrAnalogy}</p>
        </ContentSection>

        <div className="mt-4 text-xs text-muted-foreground">
          来源：{data.source}
        </div>
      </CardContent>
    </Card>
  )
}

export default function FavoritesPage({ onBack }: FavoritesPageProps) {
  const [favorites, setFavorites] = useState<PhilosophyItem[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadFavorites = () => {
      const favs = getFavorites()
      setFavorites(favs)
      setIsLoading(false)
    }

    loadFavorites()
  }, [])

  const handleRemoveFavorite = (id: number) => {
    removeFromFavorites(id)
    setFavorites(prev => prev.filter(fav => fav.id !== id))
  }

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载收藏中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen flex-col bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="icon"
            onClick={onBack}
            className="rounded-full"
          >
            <ArrowLeft className="h-5 w-5" />
            <span className="sr-only">返回</span>
          </Button>
          <div className="flex items-center gap-2">
            <Heart className="h-5 w-5 text-red-500" />
            <h1 className="text-xl font-bold text-foreground">我的收藏</h1>
          </div>
        </div>
        <div className="text-sm text-muted-foreground">
          共 {favorites.length} 张卡片
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {favorites.length === 0 ? (
          <div className="flex h-full items-center justify-center">
            <div className="text-center">
              <Heart className="h-16 w-16 text-muted-foreground/50 mx-auto mb-4" />
              <h2 className="text-lg font-semibold text-foreground mb-2">还没有收藏</h2>
              <p className="text-muted-foreground">
                在卡片底部点击心形图标来收藏您喜欢的哲学启迪
              </p>
            </div>
          </div>
        ) : (
          <div className="grid gap-4 max-w-2xl mx-auto">
            {favorites.map((favorite) => (
              <FavoriteCard
                key={favorite.id}
                data={favorite}
                onRemove={handleRemoveFavorite}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
